import { Component, inject } from '@angular/core';
import { OnInit } from '@angular/core';
import { RecipeService } from '../service/recipe/recipe';
import { Recipe } from '../model/Recipe';
import {RecipeComponent} from '../recipe/recipe';

@Component({
  selector: 'app-recipe-list',
  imports: [RecipeComponent],
  templateUrl: './recipe-list.html',
  styleUrl: './recipe-list.scss'
})
export class RecipeList implements OnInit {

  private readonly recipeService: RecipeService= inject(RecipeService)
  recipes?: Recipe[];
  loading = false;
  error: string | null = null;

  ngOnInit(): void {
    this.loadRecipes();
  }

  loadRecipes(): void {
    this.loading = true;
    this.error = null;

    this.recipeService.getRecipes().subscribe({
      next: (recipes: Recipe[]) => {
        this.recipes = recipes;
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Erreur lors du chargement des recettes';
        this.loading = false;
        console.error('Erreur lors du chargement des recettes:', error);
      }
    });
  }

  deleteRecipe(recipeId: number): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette recette ?')) {
      this.recipeService.deleteRecipe(recipeId).subscribe({
        next: () => {
          // Supprimer la recette de la liste locale
          this.recipes = this.recipes?.filter(recipe => recipe.id !== recipeId);
          console.log('Recette supprimée avec succès');
        },
        error: (error) => {
          this.error = error.message || 'Erreur lors de la suppression de la recette';
          console.error('Erreur lors de la suppression:', error);
        }
      });
    }
  }
}
 