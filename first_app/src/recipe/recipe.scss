// Carte de recette principale
.example-card {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease;
  overflow: hidden;
  background: white;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15) !important;
  }
}

// En-tête de la carte
.mat-mdc-card-header {
  background: linear-gradient(135deg, #5f259f 0%, #8e44ad 100%);
  color: white !important;
  padding: 1.5rem !important;

  .mat-mdc-card-title {
    color: white !important;
    font-size: 1.4rem !important;
    font-weight: 600 !important;
    margin: 0 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

// Image de la carte
.mat-mdc-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

// Contenu de la carte
.mat-mdc-card-content {
  padding: 1.5rem !important;

  p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;

    &:first-of-type {
      font-size: 1rem;
      margin-bottom: 1.5rem;
    }
  }

  // Liste des ingrédients
  ul {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    border-left: 4px solid #5f259f;

    li {
      color: #555;
      margin-bottom: 0.5rem;
      padding-left: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      &::marker {
        color: #5f259f;
      }
    }
  }
}

// Actions de la carte
.mat-mdc-card-actions {
  padding: 1rem 1.5rem 1.5rem !important;
  display: flex !important;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: space-between;
  border-top: 1px solid #eee;
  min-height: 60px; // Pour s'assurer qu'il y a de l'espace

  button {
    border-radius: 20px !important;
    font-weight: 500 !important;
    text-transform: none !important;
    transition: all 0.3s ease;

    &.mat-mdc-button {
      background: linear-gradient(45deg, #5f259f, #8e44ad);
      color: white !important;

      &:hover {
        background: linear-gradient(45deg, #4a1e7e, #7d3c98);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(95, 37, 159, 0.3);
      }
    }

    &:not(.mat-mdc-button) {
      background: #f8f9fa;
      color: #5f259f;
      border: 2px solid #5f259f;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: 500;
      cursor: pointer;

      &:hover {
        background: #5f259f;
        color: white;
        transform: translateY(-2px);
      }
    }

    &.delete-button {
      background: linear-gradient(45deg, #e74c3c, #c0392b) !important;
      color: white !important;
      border: none !important;
      padding: 0.5rem 1rem !important;
      border-radius: 20px !important;
      font-weight: 500 !important;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;

      &:hover {
        background: linear-gradient(45deg, #c0392b, #a93226) !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
      }
    }
  }
}



// Responsive design
@media (max-width: 480px) {
  .example-card {
    max-width: 100%;
  }

  .mat-mdc-card-actions {
    flex-direction: column;

    button {
      width: 100%;
      margin-bottom: 0.5rem;
    }
  }
}
