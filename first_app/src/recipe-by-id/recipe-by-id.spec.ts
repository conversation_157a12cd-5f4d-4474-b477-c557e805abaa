import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideHttpClient } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { RecipeById } from './recipe-by-id';

describe('RecipeById', () => {
  let component: RecipeById;
  let fixture: ComponentFixture<RecipeById>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RecipeById],
      providers: [
        provideHttpClient(),
        {
          provide: ActivatedRoute,
          useValue: {
            params: of({ id: '1' })
          }
        }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(RecipeById);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
