import { Component } from '@angular/core';
import { OnInit } from '@angular/core';
import { RecipeService } from '../service/recipe/recipe';
import { Recipe } from '../model/Recipe';
import { inject } from '@angular/core';
import {RecipeComponent} from '../recipe/recipe';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-recipe-by-id',
  imports: [RecipeComponent],
  templateUrl: './recipe-by-id.html',
  styleUrl: './recipe-by-id.scss'
})
export class RecipeById implements OnInit {
  private readonly recipeService: RecipeService= inject(RecipeService)
  recipe?: Recipe;
  loading = false;
  error: string | null = null;

  constructor(private activateRoute: ActivatedRoute) {}

  ngOnInit(): void {
    this.activateRoute.params.subscribe((params) => {
      this.loadRecipe(+params['id']);
    });
  }

  loadRecipe(id: number): void {
    this.loading = true;
    this.error = null;

    this.recipeService.getRecipe(id).subscribe({
      next: (recipe: Recipe) => {
        this.recipe = recipe;
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Erreur lors du chargement de la recette';
        this.loading = false;
        console.error('Erreur lors du chargement de la recette:', error);
      }
    });
  }
}
