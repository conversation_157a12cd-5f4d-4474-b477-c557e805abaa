import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormControl, FormArray, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { RecipeService, CreateRecipeRequest } from '../service/recipe/recipe';
import { Ingredient } from '../model/Ingredient';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-add-recipe',
  imports: [ReactiveFormsModule, FormsModule, CommonModule ],
  templateUrl: './add-recipe.html',
  styleUrl: './add-recipe.scss'
})
export class AddRecipe implements OnInit {
  recipeForm!: FormGroup;
  ingredientsList: Ingredient[] = []; // à remplir via un service plus tard
  isSubmitting = false;
  submitError: string | null = null;
  submitSuccess = false;

  constructor(private fb: FormBuilder, private recipeService: RecipeService) {}

  // Validateur personnalisé pour les URLs
  private urlValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;

    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    const valid = urlPattern.test(control.value);
    return valid ? null : { invalidUrl: true };
  }

  // Validateur pour la longueur minimale des FormArrays
  private minArrayLengthValidator(minLength: number) {
    return (control: AbstractControl): ValidationErrors | null => {
      if (control instanceof FormArray) {
        return control.length >= minLength ? null : { minArrayLength: { requiredLength: minLength, actualLength: control.length } };
      }
      return null;
    };
  }



  ngOnInit(): void {
    this.initializeForm();
    this.loadIngredientsList();
  }

  private initializeForm(): void {
    this.recipeForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      picture: ['', [Validators.required, this.urlValidator]],
      description: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(500)]],
      ingredients: this.fb.array([], [this.minArrayLengthValidator(1)]),
      instructions: this.fb.array([], [this.minArrayLengthValidator(1)])
    });

    // Ajouter une instruction par défaut
    this.addInstruction();
  }

  private loadIngredientsList(): void {
    this.ingredientsList = [
      { id: 1, name: "jus d'orange" },
      { id: 2, name: "whisky" },
      { id: 3, name: "vodka" }
    ];
  }

  // === GESTION DES INGRÉDIENTS ===

  get ingredientsFormArray(): FormArray {
    return this.recipeForm.get('ingredients') as FormArray;
  }

  private createIngredientFormGroup(ingredient: Ingredient): FormGroup {
    return this.fb.group({
      ingredient: [ingredient, [Validators.required]],
      quantity: [null, [Validators.required, Validators.min(0.1), Validators.pattern(/^\d+(\.\d{1,2})?$/)]],
      unit: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(20)]]
    });
  }

  addIngredient(ingredient: Ingredient): void {
    if (!ingredient) {
      console.error('Tentative d\'ajout d\'un ingrédient null');
      return;
    }

    // Vérifier si l'ingrédient n'est pas déjà ajouté
    const alreadyExists = this.ingredientsFormArray.controls.some(
      control => control.get('ingredient')?.value?.id === ingredient.id
    );

    if (alreadyExists) {
      alert(`L'ingrédient "${ingredient.name}" a déjà été ajouté !`);
      return;
    }

    const ingredientGroup = this.createIngredientFormGroup(ingredient);
    this.ingredientsFormArray.push(ingredientGroup);

    // Forcer la mise à jour de la validation
    this.ingredientsFormArray.markAsTouched();
    this.ingredientsFormArray.updateValueAndValidity();

    console.log(`Ingrédient "${ingredient.name}" ajouté. Total: ${this.ingredientsFormArray.length}`);
  }

  onIngredientSelect(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    const selectedId = selectElement.value;

    if (!selectedId) {
      return;
    }

    const ingredient = this.ingredientsList.find(i => i.id === +selectedId);

    if (ingredient) {
      this.addIngredient(ingredient);
      // Remettre le dropdown à sa valeur par défaut
      selectElement.value = '';
    } else {
      console.error('Ingrédient non trouvé avec l\'ID:', selectedId);
    }
  }

  removeIngredient(index: number): void {
    if (index >= 0 && index < this.ingredientsFormArray.length) {
      const removedIngredient = this.ingredientsFormArray.at(index).get('ingredient')?.value;
      this.ingredientsFormArray.removeAt(index);

      // Forcer la mise à jour de la validation
      this.ingredientsFormArray.markAsTouched();
      this.ingredientsFormArray.updateValueAndValidity();

      console.log(`Ingrédient "${removedIngredient?.name}" supprimé. Total: ${this.ingredientsFormArray.length}`);
    }
  }

  isIngredientFieldInvalid(index: number, fieldName: string): boolean {
    const control = this.ingredientsFormArray.at(index)?.get(fieldName);
    return !!(control && control.invalid && (control.dirty || control.touched));
  }

  getIngredientFieldError(index: number, fieldName: string): string | null {
    const control = this.ingredientsFormArray.at(index)?.get(fieldName);
    if (!control || !control.errors) return null;

    if (control.errors['required']) return `${fieldName} est requis`;
    if (control.errors['min']) return `${fieldName} doit être supérieur à 0`;
    if (control.errors['pattern']) return `${fieldName} doit être un nombre valide`;
    if (control.errors['minlength']) return `${fieldName} trop court`;
    if (control.errors['maxlength']) return `${fieldName} trop long`;

    return 'Erreur de validation';
  }

  // === GESTION DES INSTRUCTIONS ===

  get instructionsFormArray(): FormArray {
    return this.recipeForm.get('instructions') as FormArray;
  }

  private createInstructionControl(value: string = ''): FormControl {
    return this.fb.control(value, [
      Validators.required,
      Validators.minLength(10),
      Validators.maxLength(500)
    ]);
  }

  addInstruction(): void {
    const instructionControl = this.createInstructionControl();
    this.instructionsFormArray.push(instructionControl);

    // Forcer la mise à jour de la validation
    this.instructionsFormArray.markAsTouched();
    this.instructionsFormArray.updateValueAndValidity();

    console.log(`Instruction ajoutée. Total: ${this.instructionsFormArray.length}`);
  }

  removeInstruction(index: number): void {
    if (index >= 0 && index < this.instructionsFormArray.length && this.instructionsFormArray.length > 1) {
      this.instructionsFormArray.removeAt(index);

      // Forcer la mise à jour de la validation
      this.instructionsFormArray.markAsTouched();
      this.instructionsFormArray.updateValueAndValidity();

      console.log(`Instruction ${index + 1} supprimée. Total: ${this.instructionsFormArray.length}`);
    }
  }

  isInstructionInvalid(index: number): boolean {
    const control = this.instructionsFormArray.at(index);
    return !!(control && control.invalid && (control.dirty || control.touched));
  }

  getInstructionError(index: number): string | null {
    const control = this.instructionsFormArray.at(index);
    if (!control || !control.errors) return null;

    if (control.errors['required']) return 'Cette étape est requise';
    if (control.errors['minlength']) return 'Cette étape doit contenir au moins 10 caractères';
    if (control.errors['maxlength']) return 'Cette étape ne peut pas dépasser 500 caractères';

    return 'Erreur de validation';
  }

  onSubmit(): void {
    if (this.recipeForm.invalid) {
      this.markFormGroupTouched(this.recipeForm);
      // Marquer spécifiquement le FormArray des ingrédients comme touché
      this.ingredientsFormArray.markAsTouched();
      return;
    }

    this.isSubmitting = true;
    this.submitError = null;
    this.submitSuccess = false;

    const formValue = this.recipeForm.value;

    // Préparer les données pour le backend (sans ID de recette, avec seulement l'ID des ingrédients)
    const createRecipeData: CreateRecipeRequest = {
      name: formValue.name,
      picture: formValue.picture,
      description: formValue.description,
      ingredients: formValue.ingredients.map((ingGroup: any) => ({
        ingredientId: ingGroup.ingredient.id,  // Seulement l'ID de l'ingrédient
        quantity: ingGroup.quantity,
        unit: ingGroup.unit
      })),
      instructions: formValue.instructions
    };

    console.log('Données envoyées au backend:', createRecipeData);

    this.recipeService.createRecipe(createRecipeData).subscribe({
      next: (res) => {
        console.log('Recette créée avec succès', res);
        this.submitSuccess = true;
        this.isSubmitting = false;
        this.resetForm();
      },
      error: (err) => {
        console.error('Erreur création recette', err);
        this.submitError = err.message || 'Erreur lors de la création de la recette';
        this.isSubmitting = false;
      }
    });
  }

  private resetForm(): void {
    // Réinitialiser les champs de base
    this.recipeForm.patchValue({
      name: '',
      picture: '',
      description: ''
    });

    // Vider complètement les FormArrays
    this.ingredientsFormArray.clear();
    this.instructionsFormArray.clear();

    // Ajouter une instruction par défaut
    this.addInstruction();

    // Marquer le formulaire comme non touché
    this.recipeForm.markAsUntouched();
    this.recipeForm.markAsPristine();

    console.log('Formulaire réinitialisé');
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        });
      }
    });
  }



}
