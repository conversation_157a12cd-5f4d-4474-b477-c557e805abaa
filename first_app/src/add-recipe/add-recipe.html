<!-- Messages de statut -->
@if (submitSuccess) {
  <div class="success-message">
    ✅ Recette créée avec succès !
  </div>
}

@if (submitError) {
  <div class="error-message">
    ❌ {{ submitError }}
  </div>
}

<form [formGroup]="recipeForm" (ngSubmit)="onSubmit()">
  <label for="name">Nom</label>
  <input id="name" formControlName="name" type="text" />
  <div *ngIf="recipeForm.get('name')?.invalid && recipeForm.get('name')?.touched" class="error">
    <div *ngIf="recipeForm.get('name')?.errors?.['required']">Le nom est requis</div>
    <div *ngIf="recipeForm.get('name')?.errors?.['minlength']">Le nom doit contenir au moins 3 caractères</div>
    <div *ngIf="recipeForm.get('name')?.errors?.['maxlength']">Le nom ne peut pas dépasser 100 caractères</div>
  </div>

  <label for="picture">Image (URL)</label>
  <input id="picture" formControlName="picture" type="text" placeholder="https://exemple.com/image.jpg" />
  <div *ngIf="recipeForm.get('picture')?.invalid && recipeForm.get('picture')?.touched" class="error">
    <div *ngIf="recipeForm.get('picture')?.errors?.['required']">L'URL de l'image est requise</div>
    <div *ngIf="recipeForm.get('picture')?.errors?.['invalidUrl']">Veuillez entrer une URL valide</div>
  </div>

  <label for="description">Description</label>
  <textarea id="description" formControlName="description" placeholder="Décrivez votre recette en quelques mots..."></textarea>
  <div *ngIf="recipeForm.get('description')?.invalid && recipeForm.get('description')?.touched" class="error">
    <div *ngIf="recipeForm.get('description')?.errors?.['required']">La description est requise</div>
    <div *ngIf="recipeForm.get('description')?.errors?.['minlength']">La description doit contenir au moins 10 caractères</div>
    <div *ngIf="recipeForm.get('description')?.errors?.['maxlength']">La description ne peut pas dépasser 500 caractères</div>
  </div>

  <!-- === SECTION INGRÉDIENTS === -->
  <div class="ingredients-section">
    <h3>Ingrédients</h3>

    <!-- Sélecteur d'ingrédients -->
    <div class="ingredient-selector">
      <label for="ingredient-select">Ajouter un ingrédient :</label>
      <select id="ingredient-select" (change)="onIngredientSelect($event)" class="ingredient-dropdown">
        <option value="">-- Sélectionner un ingrédient --</option>
        <option *ngFor="let ingredient of ingredientsList" [value]="ingredient.id">
          {{ ingredient.name }}
        </option>
      </select>
    </div>

    <!-- Liste des ingrédients ajoutés -->
    <div class="ingredients-list" formArrayName="ingredients">
      @if (ingredientsFormArray.length === 0) {
        <div class="empty-ingredients">
          <p>Aucun ingrédient ajouté. Sélectionnez un ingrédient ci-dessus pour commencer.</p>
        </div>
      }

      @for (ingredientGroup of ingredientsFormArray.controls; track $index; let i = $index) {
        <div class="ingredient-item" [formGroupName]="i">
          <div class="ingredient-header">
            <h4>{{ ingredientGroup.get('ingredient')?.value?.name }}</h4>
            <button type="button" class="remove-btn" (click)="removeIngredient(i)" title="Supprimer cet ingrédient">
              ✕
            </button>
          </div>

          <div class="ingredient-inputs">
            <div class="input-group">
              <label [for]="'quantity-' + i">Quantité *</label>
              <input
                [id]="'quantity-' + i"
                type="number"
                formControlName="quantity"
                placeholder="Ex: 250"
                min="0.1"
                step="0.1"
                [class.invalid]="isIngredientFieldInvalid(i, 'quantity')"
              />
              @if (isIngredientFieldInvalid(i, 'quantity')) {
                <div class="error-message">{{ getIngredientFieldError(i, 'quantity') }}</div>
              }
            </div>

            <div class="input-group">
              <label [for]="'unit-' + i">Unité *</label>
              <input
                [id]="'unit-' + i"
                type="text"
                formControlName="unit"
                placeholder="Ex: g, ml, cuillères..."
                maxlength="20"
                [class.invalid]="isIngredientFieldInvalid(i, 'unit')"
              />
              @if (isIngredientFieldInvalid(i, 'unit')) {
                <div class="error-message">{{ getIngredientFieldError(i, 'unit') }}</div>
              }
            </div>
          </div>
        </div>
      }
    </div>

    <!-- Message d'erreur global pour les ingrédients -->
    @if (ingredientsFormArray.invalid && ingredientsFormArray.touched) {
      <div class="global-error">
        @if (ingredientsFormArray.errors?.['minArrayLength']) {
          <div class="error-message">⚠️ Au moins un ingrédient est requis pour créer une recette.</div>
        }
      </div>
    }
  </div>

  <!-- === SECTION INSTRUCTIONS === -->
  <div class="instructions-section">
    <h3>Instructions de préparation</h3>

    <div class="instructions-list" formArrayName="instructions">
      @for (instructionControl of instructionsFormArray.controls; track $index; let i = $index) {
        <div class="instruction-item">
          <div class="instruction-header">
            <label [for]="'instruction-' + i">Étape {{ i + 1 }} *</label>
            @if (instructionsFormArray.length > 1) {
              <button
                type="button"
                class="remove-btn"
                (click)="removeInstruction(i)"
                title="Supprimer cette étape"
              >
                ✕
              </button>
            }
          </div>

          <div class="instruction-input">
            <textarea
              [id]="'instruction-' + i"
              [formControlName]="i"
              placeholder="Décrivez cette étape en détail (minimum 10 caractères)..."
              rows="3"
              maxlength="500"
              [class.invalid]="isInstructionInvalid(i)"
            ></textarea>

            @if (isInstructionInvalid(i)) {
              <div class="error-message">{{ getInstructionError(i) }}</div>
            }

            <div class="char-counter">
              {{ instructionControl.value?.length || 0 }}/500 caractères
            </div>
          </div>
        </div>
      }
    </div>

    <button type="button" class="add-instruction-btn" (click)="addInstruction()">
      ➕ Ajouter une étape
    </button>

    <!-- Message d'erreur global pour les instructions -->
    @if (instructionsFormArray.invalid && instructionsFormArray.touched) {
      <div class="global-error">
        @if (instructionsFormArray.errors?.['minArrayLength']) {
          <div class="error-message">⚠️ Au moins une instruction est requise pour créer une recette.</div>
        }
      </div>
    }
  </div>

  <!-- === BOUTON DE SOUMISSION === -->
  <div class="submit-section">
    <button type="submit" [disabled]="recipeForm.invalid || isSubmitting" class="submit-btn">
      @if (isSubmitting) {
        <span class="loading">⏳ Création en cours...</span>
      } @else {
        <span>✨ Créer la recette</span>
      }
    </button>

    <!-- Indicateur de statut du formulaire -->
    <div class="form-status">
      <div class="status-item">
        <span class="label">Formulaire:</span>
        <span class="value" [class.valid]="recipeForm.valid" [class.invalid]="!recipeForm.valid">
          {{ recipeForm.valid ? '✅ Valide' : '❌ Incomplet' }}
        </span>
      </div>
      <div class="status-item">
        <span class="label">Ingrédients:</span>
        <span class="value">{{ ingredientsFormArray.length }} ajouté(s)</span>
      </div>
      <div class="status-item">
        <span class="label">Instructions:</span>
        <span class="value">{{ instructionsFormArray.length }} étape(s)</span>
      </div>
    </div>
  </div>
</form>
