# 🍹 Application Cocktail - React

Une application moderne de gestion de recettes de cocktails développée avec React, TypeScript et Material-UI. Cette application est une recréation en React d'une application Angular existante.

## 📋 Table des matières

- [Fonctionnalités](#fonctionnalités)
- [Technologies utilisées](#technologies-utilisées)
- [Installation](#installation)
- [Utilisation](#utilisation)
- [Structure du projet](#structure-du-projet)
- [Tests](#tests)
- [Développement](#développement)
- [API](#api)
- [Contribution](#contribution)

## ✨ Fonctionnalités

- **Liste des recettes** : Affichage en grille responsive de toutes les recettes
- **Détail des recettes** : Vue détaillée avec ingrédients et instructions étape par étape
- **Ajout de recettes** : Formulaire dynamique pour créer de nouvelles recettes
- **Gestion des ingrédients** : Ajout/suppression dynamique d'ingrédients avec quantités et unités
- **Instructions étape par étape** : Gestion dynamique des étapes de préparation
- **Suppression de recettes** : Suppression avec confirmation
- **Navigation fluide** : Routing avec React Router
- **Interface moderne** : Design Material-UI responsive
- **Gestion d'état** : États de chargement et gestion d'erreurs
- **Tests unitaires** : Couverture de tests avec Vitest et React Testing Library

## 🛠 Technologies utilisées

- **React 18** - Framework frontend
- **TypeScript** - Typage statique
- **Material-UI (MUI)** - Bibliothèque de composants UI
- **React Router** - Navigation côté client
- **Vite** - Outil de build et serveur de développement
- **Vitest** - Framework de tests
- **React Testing Library** - Tests de composants
- **ESLint** - Linting du code

## 🚀 Installation

### Prérequis

- Node.js (version 18 ou supérieure)
- npm ou yarn

### Étapes d'installation

1. **Cloner le repository**
   ```bash
   git clone <url-du-repository>
   cd projet-react
   ```

2. **Installer les dépendances**
   ```bash
   npm install
   ```

3. **Lancer l'application en mode développement**
   ```bash
   npm run dev
   ```

4. **Ouvrir l'application**

   L'application sera disponible à l'adresse : `http://localhost:5173`

## 📱 Utilisation

### Navigation

- **Page d'accueil** (`/`) : Redirige automatiquement vers la liste des recettes
- **Liste des recettes** (`/recipes`) : Affiche toutes les recettes disponibles
- **Détail d'une recette** (`/recipe/:id`) : Affiche les détails d'une recette spécifique
- **Ajouter une recette** (`/recipe/add`) : Formulaire pour créer une nouvelle recette

### Fonctionnalités principales

#### Consulter les recettes
- Parcourez la liste des recettes en grille
- Cliquez sur "Voir ingrédients" pour afficher/masquer les ingrédients
- Cliquez sur "Voir détails" pour accéder à la vue détaillée

#### Ajouter une recette
1. Cliquez sur le bouton "+" flottant ou naviguez vers `/recipe/add`
2. Remplissez les informations de base (nom, description, image)
3. Ajoutez les ingrédients avec leurs quantités et unités
4. Ajoutez les instructions étape par étape
5. Cliquez sur "Créer la recette"

#### Supprimer une recette
1. Cliquez sur l'icône de suppression (poubelle) sur une carte de recette
2. Confirmez la suppression dans la boîte de dialogue

## 📁 Structure du projet

```
projet-react/
├── public/                 # Fichiers statiques
├── src/
│   ├── assets/            # Données mock et ressources
│   │   └── recipe-mock.ts # Données de test et service mock
│   ├── components/        # Composants React réutilisables
│   │   ├── __tests__/     # Tests des composants
│   │   ├── AddRecipe.tsx  # Formulaire d'ajout de recette
│   │   ├── Recipe.tsx     # Carte de recette individuelle
│   │   ├── RecipeDetail.tsx # Vue détaillée d'une recette
│   │   └── RecipeList.tsx # Liste/grille des recettes
│   ├── models/            # Interfaces TypeScript
│   │   ├── Ingredient.ts  # Modèle d'ingrédient
│   │   ├── Recipe.ts      # Modèle de recette
│   │   └── RecipeIngredient.ts # Modèle de liaison recette-ingrédient
│   ├── pages/             # Composants de pages avec routing
│   │   ├── AddRecipePage.tsx
│   │   ├── RecipeDetailPage.tsx
│   │   └── RecipeListPage.tsx
│   ├── services/          # Services et API
│   │   ├── __tests__/     # Tests des services
│   │   └── recipeService.ts # Service de gestion des recettes
│   ├── test/              # Configuration des tests
│   │   └── setup.ts       # Configuration Vitest
│   ├── App.tsx            # Composant principal avec routing
│   ├── App.css            # Styles globaux
│   └── main.tsx           # Point d'entrée de l'application
├── package.json           # Dépendances et scripts
├── vite.config.ts         # Configuration Vite et tests
└── README.md              # Documentation
```

## 🧪 Tests

Le projet inclut une suite de tests unitaires complète.

### Lancer les tests

```bash
# Lancer tous les tests
npm run test

# Lancer les tests en mode watch
npm run test:watch

# Lancer les tests une seule fois
npm run test:run

# Lancer les tests avec interface UI
npm run test:ui
```

### Couverture de tests

- **Composants** : Tests d'interaction, rendu et props
- **Services** : Tests des opérations CRUD et gestion d'erreurs
- **Mocks** : Simulation des appels API avec délais réalistes

## 🔧 Développement

### Scripts disponibles

```bash
npm run dev          # Serveur de développement
npm run build        # Build de production
npm run preview      # Prévisualisation du build
npm run lint         # Linting du code
npm run test         # Tests unitaires
```

### Ajout de nouvelles fonctionnalités

1. **Nouveaux composants** : Placez-les dans `src/components/`
2. **Nouveaux modèles** : Ajoutez-les dans `src/models/`
3. **Nouveaux services** : Placez-les dans `src/services/`
4. **Tests** : Ajoutez les tests correspondants dans les dossiers `__tests__/`

### Conventions de code

- Utilisez TypeScript pour tous les nouveaux fichiers
- Suivez les conventions de nommage React (PascalCase pour les composants)
- Ajoutez des tests pour toute nouvelle fonctionnalité
- Utilisez Material-UI pour l'interface utilisateur

## 🔌 API

L'application utilise actuellement un service mock (`MockRecipeService`) qui simule une API REST.

### Endpoints simulés

- `GET /recipes` - Récupérer toutes les recettes
- `GET /recipes/:id` - Récupérer une recette par ID
- `POST /recipes` - Créer une nouvelle recette
- `DELETE /recipes/:id` - Supprimer une recette

### Migration vers une vraie API

Pour connecter l'application à une vraie API :

1. Modifiez `src/services/recipeService.ts`
2. Remplacez `mockRecipeService` par `recipeService` dans les composants
3. Configurez les URLs d'API dans les variables d'environnement

## 🤝 Contribution

1. Fork le projet
2. Créez une branche pour votre fonctionnalité (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Committez vos changements (`git commit -m 'Ajout d'une nouvelle fonctionnalité'`)
4. Poussez vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Ouvrez une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🙏 Remerciements

- Application originale développée en Angular
- Material-UI pour les composants UI
- React Testing Library pour les outils de test
- Vite pour l'excellent tooling de développement

---

**Développé avec ❤️ en React et TypeScript**