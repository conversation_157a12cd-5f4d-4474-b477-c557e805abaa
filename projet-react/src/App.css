/* Styles globaux pour l'application de recettes */
.App {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* Styles pour les cartes de recettes */
.recipe-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.recipe-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Styles pour les images de recettes */
.recipe-image {
  transition: transform 0.3s ease-in-out;
}

.recipe-image:hover {
  transform: scale(1.05);
}

/* Styles pour les formulaires */
.form-section {
  margin-bottom: 2rem;
}

.ingredient-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid #e0e0e0;
  transition: border-color 0.2s ease-in-out;
}

.ingredient-item:hover {
  border-color: #1976d2;
}

/* Styles pour les boutons flottants */
.fab-button {
  position: fixed !important;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

/* Styles pour les états de chargement */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}

/* Styles pour les messages d'erreur */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

/* Styles pour les listes d'instructions */
.instruction-list {
  counter-reset: instruction-counter;
}

.instruction-item {
  counter-increment: instruction-counter;
  position: relative;
  padding-left: 3rem;
  margin-bottom: 1rem;
}

.instruction-item::before {
  content: counter(instruction-counter);
  position: absolute;
  left: 0;
  top: 0;
  background: #1976d2;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.875rem;
}

/* Responsive design */
@media (max-width: 600px) {
  .recipe-grid {
    grid-template-columns: 1fr;
  }

  .fab-button {
    bottom: 16px;
    right: 16px;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Styles pour les chips d'ingrédients */
.ingredient-chip {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

/* Styles pour les en-têtes de section */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

/* Styles pour les conteneurs vides */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
}

.empty-state h3 {
  margin-bottom: 1rem;
  color: #999;
}

/* Styles pour les boutons d'action */
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

@media (max-width: 600px) {
  .action-buttons {
    flex-direction: column;
  }
}
