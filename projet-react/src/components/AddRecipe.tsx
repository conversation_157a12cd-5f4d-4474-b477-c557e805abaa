import React, { useState } from 'react';
import {
  Con<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Box,
  Card,
  CardContent,
  IconButton,
  Alert,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import type { Ingredient } from '../models/Ingredient';
import type { CreateRecipeRequest } from '../services/recipeService';
import { mockRecipeService } from '../assets/recipe-mock';

interface AddRecipeProps {
  onBack?: () => void;
  onRecipeCreated?: () => void;
}

interface RecipeIngredientForm {
  ingredient: Ingredient | null;
  quantity: number;
  unit: string;
}

const AddRecipe: React.FC<AddRecipeProps> = ({ onBack, onRecipeCreated }) => {
  const [formData, setFormData] = useState({
    name: '',
    picture: '',
    description: '',
  });
  
  const [ingredients, setIngredients] = useState<RecipeIngredientForm[]>([
    { ingredient: null, quantity: 0, unit: '' }
  ]);
  
  const [instructions, setInstructions] = useState<string[]>(['']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Liste d'ingrédients disponibles (en réalité, cela viendrait d'une API)
  const availableIngredients: Ingredient[] = [
    { id: 1, name: "jus d'orange" },
    { id: 2, name: "whisky" },
    { id: 3, name: "vodka" },
    { id: 4, name: "rhum blanc" },
    { id: 5, name: "citron vert" },
    { id: 6, name: "menthe fraîche" },
    { id: 7, name: "sucre de canne" },
    { id: 8, name: "eau gazeuse" },
    { id: 9, name: "tequila" },
    { id: 10, name: "triple sec" },
    { id: 11, name: "sel" },
    { id: 12, name: "absinthe" },
    { id: 13, name: "sucre" },
    { id: 14, name: "eau de source glacée" },
  ];

  const handleInputChange = (field: keyof typeof formData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const addIngredient = () => {
    setIngredients(prev => [...prev, { ingredient: null, quantity: 0, unit: '' }]);
  };

  const removeIngredient = (index: number) => {
    if (ingredients.length > 1) {
      setIngredients(prev => prev.filter((_, i) => i !== index));
    }
  };

  const updateIngredient = (index: number, field: keyof RecipeIngredientForm, value: any) => {
    setIngredients(prev => prev.map((ing, i) => 
      i === index ? { ...ing, [field]: value } : ing
    ));
  };

  const addInstruction = () => {
    setInstructions(prev => [...prev, '']);
  };

  const removeInstruction = (index: number) => {
    if (instructions.length > 1) {
      setInstructions(prev => prev.filter((_, i) => i !== index));
    }
  };

  const updateInstruction = (index: number, value: string) => {
    setInstructions(prev => prev.map((inst, i) => 
      i === index ? value : inst
    ));
  };

  const validateForm = (): string | null => {
    if (!formData.name.trim()) return "Le nom de la recette est requis";
    if (!formData.description.trim()) return "La description est requise";
    if (!formData.picture.trim()) return "L'URL de l'image est requise";
    
    const validIngredients = ingredients.filter(ing => 
      ing.ingredient && ing.quantity > 0 && ing.unit.trim()
    );
    if (validIngredients.length === 0) return "Au moins un ingrédient est requis";
    
    const validInstructions = instructions.filter(inst => inst.trim());
    if (validInstructions.length === 0) return "Au moins une instruction est requise";
    
    return null;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const validIngredients = ingredients.filter(ing => 
        ing.ingredient && ing.quantity > 0 && ing.unit.trim()
      );
      
      const validInstructions = instructions.filter(inst => inst.trim());

      const recipeData: CreateRecipeRequest = {
        name: formData.name.trim(),
        picture: formData.picture.trim(),
        description: formData.description.trim(),
        ingredients: validIngredients.map(ing => ({
          ingredientId: ing.ingredient!.id!,
          quantity: ing.quantity,
          unit: ing.unit.trim(),
        })),
        instructions: validInstructions,
      };

      await mockRecipeService.createRecipe(recipeData);
      setSuccess(true);
      
      // Réinitialiser le formulaire après un délai
      setTimeout(() => {
        if (onRecipeCreated) {
          onRecipeCreated();
        }
      }, 2000);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la création de la recette';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="50vh">
          <Alert severity="success" sx={{ mb: 2 }}>
            ✅ Recette créée avec succès !
          </Alert>
          <Button
            variant="contained"
            onClick={onBack}
            startIcon={<ArrowBackIcon />}
          >
            Retour à la liste
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      {/* Header */}
      <Box display="flex" alignItems="center" mb={3}>
        {onBack && (
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={onBack}
            sx={{ mr: 2 }}
          >
            Retour
          </Button>
        )}
        <Typography variant="h4" component="h1">
          Ajouter une recette
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Informations générales
            </Typography>
            
            <Box display="flex" flexDirection="column" gap={2}>
              <TextField
                label="Nom de la recette"
                value={formData.name}
                onChange={handleInputChange('name')}
                required
                fullWidth
              />
              
              <TextField
                label="URL de l'image"
                value={formData.picture}
                onChange={handleInputChange('picture')}
                required
                fullWidth
                placeholder="https://example.com/image.jpg"
              />
              
              <TextField
                label="Description"
                value={formData.description}
                onChange={handleInputChange('description')}
                required
                fullWidth
                multiline
                rows={3}
              />
            </Box>
          </CardContent>
        </Card>

        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Ingrédients ({ingredients.length})
              </Typography>
              <Button
                startIcon={<AddIcon />}
                onClick={addIngredient}
                variant="outlined"
                size="small"
              >
                Ajouter
              </Button>
            </Box>
            
            {ingredients.map((ingredient, index) => (
              <Box key={index} display="flex" gap={2} alignItems="center" mb={2}>
                <FormControl sx={{ minWidth: 200 }}>
                  <InputLabel>Ingrédient</InputLabel>
                  <Select
                    value={ingredient.ingredient?.id || ''}
                    onChange={(e) => {
                      const selectedIngredient = availableIngredients.find(
                        ing => ing.id === e.target.value
                      );
                      updateIngredient(index, 'ingredient', selectedIngredient || null);
                    }}
                    label="Ingrédient"
                  >
                    {availableIngredients.map((ing) => (
                      <MenuItem key={ing.id} value={ing.id}>
                        {ing.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                
                <TextField
                  label="Quantité"
                  type="number"
                  value={ingredient.quantity || ''}
                  onChange={(e) => updateIngredient(index, 'quantity', parseFloat(e.target.value) || 0)}
                  sx={{ width: 120 }}
                />
                
                <TextField
                  label="Unité"
                  value={ingredient.unit}
                  onChange={(e) => updateIngredient(index, 'unit', e.target.value)}
                  sx={{ width: 120 }}
                  placeholder="cl, g, pièce..."
                />
                
                <IconButton
                  onClick={() => removeIngredient(index)}
                  disabled={ingredients.length === 1}
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            ))}
          </CardContent>
        </Card>

        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Instructions ({instructions.length})
              </Typography>
              <Button
                startIcon={<AddIcon />}
                onClick={addInstruction}
                variant="outlined"
                size="small"
              >
                Ajouter
              </Button>
            </Box>
            
            {instructions.map((instruction, index) => (
              <Box key={index} display="flex" gap={2} alignItems="flex-start" mb={2}>
                <Chip
                  label={index + 1}
                  size="small"
                  color="primary"
                  sx={{ mt: 1 }}
                />
                <TextField
                  label={`Étape ${index + 1}`}
                  value={instruction}
                  onChange={(e) => updateInstruction(index, e.target.value)}
                  fullWidth
                  multiline
                  rows={2}
                />
                <IconButton
                  onClick={() => removeInstruction(index)}
                  disabled={instructions.length === 1}
                  color="error"
                  sx={{ mt: 1 }}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            ))}
          </CardContent>
        </Card>

        <Box display="flex" justifyContent="flex-end" gap={2}>
          {onBack && (
            <Button
              variant="outlined"
              onClick={onBack}
              disabled={loading}
            >
              Annuler
            </Button>
          )}
          <Button
            type="submit"
            variant="contained"
            startIcon={<SaveIcon />}
            disabled={loading}
          >
            {loading ? 'Création...' : 'Créer la recette'}
          </Button>
        </Box>
      </form>
    </Container>
  );
};

export default AddRecipe;
