import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Box,
  Fab,
} from '@mui/material';
import { Add as AddIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import Recipe from './Recipe';
import type { Recipe as RecipeType } from '../models/Recipe';
import { mockRecipeService } from '../assets/recipe-mock';

interface RecipeListProps {
  onAddRecipe?: () => void;
  onViewRecipeDetails?: (recipeId: number) => void;
}

const RecipeList: React.FC<RecipeListProps> = ({
  onAddRecipe,
  onViewRecipeDetails,
}) => {
  console.log('🎯 RecipeList component rendered');
  const [recipes, setRecipes] = useState<RecipeType[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadRecipes = async () => {
    console.log('🔄 Début du chargement des recettes...');
    setLoading(true);
    setError(null);

    try {
      console.log('📡 Appel du service mock...');
      const fetchedRecipes = await mockRecipeService.getRecipes();
      console.log('✅ Recettes récupérées:', fetchedRecipes);
      setRecipes(fetchedRecipes);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement des recettes';
      console.error('❌ Erreur lors du chargement des recettes:', err);
      setError(errorMessage);
    } finally {
      console.log('🏁 Fin du chargement');
      setLoading(false);
    }
  };

  const handleDeleteRecipe = async (recipeId: number) => {
    try {
      await mockRecipeService.deleteRecipe(recipeId);
      // Supprimer la recette de la liste locale
      setRecipes(prevRecipes => prevRecipes.filter(recipe => recipe.id !== recipeId));
      console.log('Recette supprimée avec succès');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la suppression de la recette';
      setError(errorMessage);
      console.error('Erreur lors de la suppression:', err);
    }
  };

  useEffect(() => {
    loadRecipes();
  }, []);

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="50vh">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Chargement des recettes...
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="50vh">
          <Alert severity="error" sx={{ mb: 2, width: '100%', maxWidth: 600 }}>
            {error}
          </Alert>
          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={loadRecipes}
          >
            Réessayer
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h3" component="h1" gutterBottom>
          Les Recettes
        </Typography>
        {onAddRecipe && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={onAddRecipe}
            size="large"
          >
            Ajouter une recette
          </Button>
        )}
      </Box>

      {recipes.length === 0 ? (
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="40vh">
          <Typography variant="h5" color="text.secondary" gutterBottom>
            Aucune recette trouvée
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Commencez par ajouter votre première recette !
          </Typography>
          {onAddRecipe && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={onAddRecipe}
              size="large"
            >
              Ajouter une recette
            </Button>
          )}
        </Box>
      ) : (
        <Box
          display="grid"
          gridTemplateColumns={{
            xs: '1fr',
            sm: 'repeat(2, 1fr)',
            md: 'repeat(3, 1fr)',
          }}
          gap={3}
        >
          {recipes.map((recipe) => (
            <Box key={recipe.id}>
              <Recipe
                recipe={recipe}
                onDelete={handleDeleteRecipe}
                onViewDetails={onViewRecipeDetails}
              />
            </Box>
          ))}
        </Box>
      )}

      {/* Bouton flottant pour ajouter une recette */}
      {onAddRecipe && recipes.length > 0 && (
        <Fab
          color="primary"
          aria-label="ajouter une recette"
          onClick={onAddRecipe}
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
          }}
        >
          <AddIcon />
        </Fab>
      )}
    </Container>
  );
};

export default RecipeList;