import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Recipe from '../Recipe';
import type { Recipe as RecipeType } from '../../models/Recipe';

const theme = createTheme();

const mockRecipe: RecipeType = {
  id: 1,
  name: "Test Recipe",
  description: "Une recette de test délicieuse",
  picture: "https://example.com/test-image.jpg",
  ingredients: [
    {
      id: 1,
      ingredient: { id: 1, name: "Test Ingredient" },
      quantity: 2,
      unit: "cups"
    },
    {
      id: 2,
      ingredient: { id: 2, name: "Another Ingredient" },
      quantity: 1,
      unit: "tsp"
    }
  ],
  instructions: [
    "Première étape de test",
    "Deuxième étape de test"
  ]
};

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('Recipe Component', () => {
  it('renders recipe name and description', () => {
    renderWithTheme(<Recipe recipe={mockRecipe} />);
    
    expect(screen.getByText('Test Recipe')).toBeInTheDocument();
    expect(screen.getByText('Une recette de test délicieuse')).toBeInTheDocument();
  });

  it('renders recipe image with correct alt text', () => {
    renderWithTheme(<Recipe recipe={mockRecipe} />);
    
    const image = screen.getByAltText('Photo de Test Recipe');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'https://example.com/test-image.jpg');
  });

  it('toggles ingredients visibility when button is clicked', () => {
    renderWithTheme(<Recipe recipe={mockRecipe} />);

    // Initially, ingredients should not be visible
    expect(screen.queryByText('Ingrédients:')).not.toBeInTheDocument();
    expect(screen.queryByText('2 cups de Test Ingredient')).not.toBeInTheDocument();

    // Click the toggle button
    const toggleButton = screen.getByText('Voir ingrédients');
    fireEvent.click(toggleButton);

    // Now ingredients should be visible
    expect(screen.getByText('Ingrédients:')).toBeInTheDocument();
    expect(screen.getByText('2 cups de Test Ingredient')).toBeInTheDocument();
    expect(screen.getByText('1 tsp de Another Ingredient')).toBeInTheDocument();

    // Button text should change
    expect(screen.getByText('Masquer ingrédients')).toBeInTheDocument();
  });

  it('calls onDelete when delete button is clicked and confirmed', () => {
    const mockOnDelete = vi.fn();
    renderWithTheme(
      <Recipe 
        recipe={mockRecipe} 
        onDelete={mockOnDelete}
        showDeleteButton={true}
      />
    );
    
    // Click delete button
    const deleteButton = screen.getByLabelText('supprimer');
    fireEvent.click(deleteButton);
    
    // Confirm deletion in dialog
    const confirmButton = screen.getByText('Supprimer');
    fireEvent.click(confirmButton);
    
    expect(mockOnDelete).toHaveBeenCalledWith(1);
  });

  it('calls onViewDetails when details button is clicked', () => {
    const mockOnViewDetails = vi.fn();
    renderWithTheme(
      <Recipe 
        recipe={mockRecipe} 
        onViewDetails={mockOnViewDetails}
        showDetailsButton={true}
      />
    );
    
    const detailsButton = screen.getByText('Voir détails');
    fireEvent.click(detailsButton);
    
    expect(mockOnViewDetails).toHaveBeenCalledWith(1);
  });

  it('does not show delete button when showDeleteButton is false', () => {
    renderWithTheme(
      <Recipe 
        recipe={mockRecipe} 
        showDeleteButton={false}
      />
    );
    
    expect(screen.queryByLabelText('supprimer')).not.toBeInTheDocument();
  });

  it('does not show details button when showDetailsButton is false', () => {
    renderWithTheme(
      <Recipe 
        recipe={mockRecipe} 
        showDetailsButton={false}
      />
    );
    
    expect(screen.queryByText('Voir détails')).not.toBeInTheDocument();
  });

  it('handles recipe without image', () => {
    const recipeWithoutImage = { ...mockRecipe, picture: undefined };
    renderWithTheme(<Recipe recipe={recipeWithoutImage} />);
    
    expect(screen.queryByAltText('Photo de Test Recipe')).not.toBeInTheDocument();
    expect(screen.getByText('Test Recipe')).toBeInTheDocument();
  });

  it('cancels delete when cancel button is clicked in dialog', async () => {
    const mockOnDelete = vi.fn();
    renderWithTheme(
      <Recipe
        recipe={mockRecipe}
        onDelete={mockOnDelete}
        showDeleteButton={true}
      />
    );

    // Click delete button
    const deleteButton = screen.getByLabelText('supprimer');
    fireEvent.click(deleteButton);

    // Verify dialog is open
    expect(screen.getByText('Confirmer la suppression')).toBeInTheDocument();

    // Cancel deletion in dialog
    const cancelButton = screen.getByText('Annuler');
    fireEvent.click(cancelButton);

    expect(mockOnDelete).not.toHaveBeenCalled();
    // Wait for dialog to close
    await new Promise(resolve => setTimeout(resolve, 100));
  });
});
