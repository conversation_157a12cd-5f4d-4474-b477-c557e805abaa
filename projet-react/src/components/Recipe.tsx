import React, { useState } from 'react';
import {
  Card,
  CardHeader,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  Typography,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { Delete as DeleteIcon, Visibility, VisibilityOff } from '@mui/icons-material';
import type { Recipe as RecipeType } from '../models/Recipe';

interface RecipeProps {
  recipe: RecipeType;
  onDelete?: (recipeId: number) => void;
  onViewDetails?: (recipeId: number) => void;
  showDeleteButton?: boolean;
  showDetailsButton?: boolean;
}

const Recipe: React.FC<RecipeProps> = ({
  recipe,
  onDelete,
  onViewDetails,
  showDeleteButton = true,
  showDetailsButton = true,
}) => {
  const [showIngredients, setShowIngredients] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const toggleIngredients = () => {
    setShowIngredients(!showIngredients);
  };

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (recipe.id && onDelete) {
      onDelete(recipe.id);
    }
    setDeleteDialogOpen(false);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
  };

  const handleViewDetails = () => {
    if (recipe.id && onViewDetails) {
      onViewDetails(recipe.id);
    }
  };

  return (
    <>
      <Card sx={{ maxWidth: 400, margin: 2 }} className="recipe-card fade-in">
        <CardHeader
          title={recipe.name}
          slotProps={{
            title: { variant: 'h6' }
          }}
          action={
            showDeleteButton && (
              <IconButton
                aria-label="supprimer"
                onClick={handleDeleteClick}
                color="error"
              >
                <DeleteIcon />
              </IconButton>
            )
          }
        />

        {recipe.picture && (
          <CardMedia
            component="img"
            height="200"
            image={recipe.picture}
            alt={`Photo de ${recipe.name}`}
            sx={{ objectFit: 'cover' }}
            className="recipe-image"
          />
        )}

        <CardContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {recipe.description}
          </Typography>

          {showIngredients && (
            <div>
              <Typography variant="h6" gutterBottom>
                Ingrédients:
              </Typography>
              <List dense>
                {recipe.ingredients.map((item) => (
                  <ListItem key={item.id} disablePadding>
                    <ListItemText
                      primary={`${item.quantity} ${item.unit} de ${item.ingredient.name}`}
                    />
                  </ListItem>
                ))}
              </List>
            </div>
          )}
        </CardContent>

        <CardActions>
          <Button
            size="small"
            onClick={toggleIngredients}
            startIcon={showIngredients ? <VisibilityOff /> : <Visibility />}
          >
            {showIngredients ? 'Masquer' : 'Voir'} ingrédients
          </Button>

          {showDetailsButton && recipe.id && (
            <Button size="small" onClick={handleViewDetails}>
              Voir détails
            </Button>
          )}
        </CardActions>
      </Card>

      {/* Dialog de confirmation de suppression */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-dialog-title"
      >
        <DialogTitle id="delete-dialog-title">
          Confirmer la suppression
        </DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir supprimer la recette "{recipe.name}" ?
            Cette action est irréversible.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Annuler</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default Recipe;