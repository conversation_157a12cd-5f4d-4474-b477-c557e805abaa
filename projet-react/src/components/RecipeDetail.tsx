import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Box,
  Card,
  CardMedia,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Divider,
  Chip,
} from '@mui/material';
import { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import type { Recipe } from '../models/Recipe';
import { mockRecipeService } from '../assets/recipe-mock';

interface RecipeDetailProps {
  recipeId: number;
  onBack?: () => void;
}

const RecipeDetail: React.FC<RecipeDetailProps> = ({ recipeId, onBack }) => {
  const [recipe, setRecipe] = useState<Recipe | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadRecipe = async (id: number) => {
    setLoading(true);
    setError(null);

    try {
      const fetchedRecipe = await mockRecipeService.getRecipe(id);
      setRecipe(fetchedRecipe);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement de la recette';
      setError(errorMessage);
      console.error('Erreur lors du chargement de la recette:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRecipe(recipeId);
  }, [recipeId]);

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="50vh">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Chargement de la recette...
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="50vh">
          <Alert severity="error" sx={{ mb: 2, width: '100%', maxWidth: 600 }}>
            {error}
          </Alert>
          <Box display="flex" gap={2}>
            <Button
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={() => loadRecipe(recipeId)}
            >
              Réessayer
            </Button>
            {onBack && (
              <Button
                variant="outlined"
                startIcon={<ArrowBackIcon />}
                onClick={onBack}
              >
                Retour
              </Button>
            )}
          </Box>
        </Box>
      </Container>
    );
  }

  if (!recipe) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="50vh">
          <Typography variant="h5" color="text.secondary">
            Recette non trouvée
          </Typography>
          {onBack && (
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={onBack}
              sx={{ mt: 2 }}
            >
              Retour à la liste
            </Button>
          )}
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      {/* Header avec bouton retour */}
      <Box display="flex" alignItems="center" mb={3}>
        {onBack && (
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={onBack}
            sx={{ mr: 2 }}
          >
            Retour
          </Button>
        )}
        <Typography variant="h4" component="h1" flexGrow={1}>
          {recipe.name}
        </Typography>
      </Box>

      <Card>
        {/* Image de la recette */}
        {recipe.picture && (
          <CardMedia
            component="img"
            height="300"
            image={recipe.picture}
            alt={`Photo de ${recipe.name}`}
            sx={{ objectFit: 'cover' }}
          />
        )}

        <CardContent>
          {/* Description */}
          <Typography variant="h6" gutterBottom>
            Description
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            {recipe.description}
          </Typography>

          <Divider sx={{ my: 3 }} />

          {/* Ingrédients */}
          <Typography variant="h6" gutterBottom>
            Ingrédients ({recipe.ingredients.length})
          </Typography>
          <List>
            {recipe.ingredients.map((item) => (
              <ListItem key={item.id} disablePadding>
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1}>
                      <Chip
                        label={`${item.quantity} ${item.unit}`}
                        size="small"
                        variant="outlined"
                      />
                      <Typography variant="body1">
                        {item.ingredient.name}
                      </Typography>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>

          <Divider sx={{ my: 3 }} />

          {/* Instructions */}
          <Typography variant="h6" gutterBottom>
            Instructions ({recipe.instructions.length} étapes)
          </Typography>
          <List>
            {recipe.instructions.map((instruction, index) => (
              <ListItem key={index} alignItems="flex-start">
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="flex-start" gap={2}>
                      <Chip
                        label={index + 1}
                        size="small"
                        color="primary"
                        sx={{ minWidth: 32 }}
                      />
                      <Typography variant="body1">
                        {instruction}
                      </Typography>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>
    </Container>
  );
};

export default RecipeDetail;
