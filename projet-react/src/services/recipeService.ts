import { Recipe } from '../models/Recipe';

// Interface pour les données à envoyer au backend lors de la création
export interface CreateRecipeRequest {
  name: string;
  picture: string;
  description: string;
  ingredients: {
    ingredientId: number;
    quantity: number;
    unit: string;
  }[];
  instructions: string[];
}

// Configuration de l'environnement
const API_BASE_URL = 'http://localhost:3000/api'; // À adapter selon votre backend

class RecipeService {
  private readonly baseUrl = API_BASE_URL;

  // Gestion des erreurs
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Promise<T> => {
      console.error(`${operation} failed:`, error);
      
      // Vous pouvez personnaliser la gestion d'erreur ici
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('Erreur de connexion au serveur');
      }
      
      if (error.status) {
        switch (error.status) {
          case 404:
            throw new Error('Ressource non trouvée');
          case 500:
            throw new Error('Erreur interne du serveur');
          default:
            throw new Error(`Erreur ${error.status}: ${error.statusText}`);
        }
      }
      
      throw new Error(error.message || 'Une erreur inattendue s\'est produite');
    };
  }

  // Méthode utilitaire pour les requêtes fetch
  private async fetchWithErrorHandling<T>(url: string, options?: RequestInit): Promise<T> {
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const error = new Error(`HTTP error! status: ${response.status}`);
        (error as any).status = response.status;
        (error as any).statusText = response.statusText;
        throw error;
      }

      return await response.json();
    } catch (error) {
      throw await this.handleError<T>('fetchWithErrorHandling')(error);
    }
  }

  // Récupérer toutes les recettes
  async getRecipes(): Promise<Recipe[]> {
    try {
      return await this.fetchWithErrorHandling<Recipe[]>(`${this.baseUrl}/recipes`);
    } catch (error) {
      throw await this.handleError<Recipe[]>('getRecipes', [])(error);
    }
  }

  // Récupérer une recette par ID
  async getRecipe(id: number): Promise<Recipe> {
    try {
      return await this.fetchWithErrorHandling<Recipe>(`${this.baseUrl}/recipes/${id}`);
    } catch (error) {
      throw await this.handleError<Recipe>('getRecipe')(error);
    }
  }

  // Créer une nouvelle recette
  async createRecipe(recipeData: CreateRecipeRequest): Promise<Recipe> {
    try {
      return await this.fetchWithErrorHandling<Recipe>(`${this.baseUrl}/recipes`, {
        method: 'POST',
        body: JSON.stringify(recipeData),
      });
    } catch (error) {
      throw await this.handleError<Recipe>('createRecipe')(error);
    }
  }

  // Mettre à jour une recette
  async updateRecipe(id: number, recipeData: Partial<CreateRecipeRequest>): Promise<Recipe> {
    try {
      return await this.fetchWithErrorHandling<Recipe>(`${this.baseUrl}/recipes/${id}`, {
        method: 'PUT',
        body: JSON.stringify(recipeData),
      });
    } catch (error) {
      throw await this.handleError<Recipe>('updateRecipe')(error);
    }
  }

  // Supprimer une recette
  async deleteRecipe(id: number): Promise<void> {
    try {
      await this.fetchWithErrorHandling<void>(`${this.baseUrl}/recipes/${id}`, {
        method: 'DELETE',
      });
    } catch (error) {
      throw await this.handleError<void>('deleteRecipe')(error);
    }
  }
}

// Export d'une instance singleton
export const recipeService = new RecipeService();
export default recipeService;
