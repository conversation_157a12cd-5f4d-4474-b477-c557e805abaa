import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mockRecipeService } from '../../assets/recipe-mock';
import type { CreateRecipeRequest } from '../recipeService';

describe('MockRecipeService', () => {
  beforeEach(() => {
    // Reset the service state before each test
    vi.clearAllMocks();
  });

  it('should get all recipes', async () => {
    const recipes = await mockRecipeService.getRecipes();
    
    expect(recipes).toBeDefined();
    expect(Array.isArray(recipes)).toBe(true);
    expect(recipes.length).toBeGreaterThan(0);
    
    // Check that each recipe has required properties
    recipes.forEach(recipe => {
      expect(recipe).toHaveProperty('id');
      expect(recipe).toHaveProperty('name');
      expect(recipe).toHaveProperty('description');
      expect(recipe).toHaveProperty('ingredients');
      expect(recipe).toHaveProperty('instructions');
    });
  });

  it('should get a specific recipe by id', async () => {
    const recipe = await mockRecipeService.getRecipe(1);
    
    expect(recipe).toBeDefined();
    expect(recipe.id).toBe(1);
    expect(recipe.name).toBe("L'Absinthe");
    expect(recipe.ingredients).toBeDefined();
    expect(recipe.instructions).toBeDefined();
  });

  it('should throw error when getting non-existent recipe', async () => {
    await expect(mockRecipeService.getRecipe(999)).rejects.toThrow('Recette avec l\'ID 999 non trouvée');
  });

  it('should create a new recipe', async () => {
    const newRecipeData: CreateRecipeRequest = {
      name: 'Test Recipe',
      picture: 'https://example.com/test.jpg',
      description: 'A test recipe',
      ingredients: [
        {
          ingredientId: 1,
          quantity: 2,
          unit: 'cups'
        }
      ],
      instructions: ['Step 1', 'Step 2']
    };

    const createdRecipe = await mockRecipeService.createRecipe(newRecipeData);
    
    expect(createdRecipe).toBeDefined();
    expect(createdRecipe.id).toBeDefined();
    expect(createdRecipe.name).toBe('Test Recipe');
    expect(createdRecipe.description).toBe('A test recipe');
    expect(createdRecipe.picture).toBe('https://example.com/test.jpg');
    expect(createdRecipe.instructions).toEqual(['Step 1', 'Step 2']);
  });

  it('should delete a recipe', async () => {
    // First, get the initial count
    const initialRecipes = await mockRecipeService.getRecipes();
    const initialCount = initialRecipes.length;
    
    // Delete a recipe
    await mockRecipeService.deleteRecipe(1);
    
    // Check that the recipe was deleted
    const updatedRecipes = await mockRecipeService.getRecipes();
    expect(updatedRecipes.length).toBe(initialCount - 1);
    
    // Verify the specific recipe is no longer there
    await expect(mockRecipeService.getRecipe(1)).rejects.toThrow('Recette avec l\'ID 1 non trouvée');
  });

  it('should throw error when deleting non-existent recipe', async () => {
    await expect(mockRecipeService.deleteRecipe(999)).rejects.toThrow('Recette avec l\'ID 999 non trouvée');
  });

  it('should simulate network delay', async () => {
    const startTime = Date.now();
    await mockRecipeService.getRecipes();
    const endTime = Date.now();
    
    // Should take at least 500ms (the simulated delay)
    expect(endTime - startTime).toBeGreaterThanOrEqual(500);
  });

  it('should assign incremental IDs to new recipes', async () => {
    const recipe1Data: CreateRecipeRequest = {
      name: 'Recipe 1',
      picture: 'https://example.com/1.jpg',
      description: 'First test recipe',
      ingredients: [],
      instructions: ['Step 1']
    };

    const recipe2Data: CreateRecipeRequest = {
      name: 'Recipe 2',
      picture: 'https://example.com/2.jpg',
      description: 'Second test recipe',
      ingredients: [],
      instructions: ['Step 1']
    };

    const createdRecipe1 = await mockRecipeService.createRecipe(recipe1Data);
    const createdRecipe2 = await mockRecipeService.createRecipe(recipe2Data);
    
    expect(createdRecipe2.id).toBeGreaterThan(createdRecipe1.id!);
  });
});
