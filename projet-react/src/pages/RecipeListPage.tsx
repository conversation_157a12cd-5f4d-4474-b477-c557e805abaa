import React from 'react';
import { useNavigate } from 'react-router-dom';
import RecipeList from '../components/RecipeList';

const RecipeListPage: React.FC = () => {
  const navigate = useNavigate();

  const handleAddRecipe = () => {
    navigate('/recipe/add');
  };

  const handleViewRecipeDetails = (recipeId: number) => {
    navigate(`/recipe/${recipeId}`);
  };

  return (
    <RecipeList
      onAddRecipe={handleAddRecipe}
      onViewRecipeDetails={handleViewRecipeDetails}
    />
  );
};

export default RecipeListPage;
