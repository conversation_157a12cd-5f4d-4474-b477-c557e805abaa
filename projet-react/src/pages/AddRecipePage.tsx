import React from 'react';
import { useNavigate } from 'react-router-dom';
import AddRecipe from '../components/AddRecipe';

const AddRecipePage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/recipes');
  };

  const handleRecipeCreated = () => {
    navigate('/recipes');
  };

  return (
    <AddRecipe
      onBack={handleBack}
      onRecipeCreated={handleRecipeCreated}
    />
  );
};

export default AddRecipePage;
