import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import RecipeDetail from '../components/RecipeDetail';

const RecipeDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const recipeId = id ? parseInt(id, 10) : 0;

  const handleBack = () => {
    navigate('/recipes');
  };

  if (!id || isNaN(recipeId)) {
    return (
      <div>
        <h1>Erreur</h1>
        <p>ID de recette invalide</p>
        <button onClick={handleBack}>Retour à la liste</button>
      </div>
    );
  }

  return (
    <RecipeDetail
      recipeId={recipeId}
      onBack={handleBack}
    />
  );
};

export default RecipeDetailPage;
