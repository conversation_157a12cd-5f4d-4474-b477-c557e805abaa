import { Recipe } from "../models/Recipe";

export const RECIPE_MOCK: Recipe = {
  id: 1,
  name: "L'Absinthe",
  description: "Une recette traditionnelle d'absinthe, à déguster avec modération et en bonne compagnie.",
  picture: "https://upload.wikimedia.org/wikipedia/commons/e/e2/Absinthe-glass.jpg",
  ingredients: [
    {
      id: 0,
      ingredient: {
        id: 1,
        name: "absinthe"
      },
      quantity: 1,
      unit: "oz"
    },
    {
      id: 1,
      ingredient: {
        id: 6,
        name: "sucre"
      },
      quantity: 1,
      unit: "cube"
    },
    {
      id: 2,
      ingredient: {
        id: 4,
        name: "eau de source glacée"
      },
      quantity: 3,
      unit: "oz"
    },
    {
      id: 3,
      ingredient: {
        id: 12,
        name: "cuillère à absinthe perforée"
      },
      quantity: 1,
      unit: "cuillère"
    }
  ],
  instructions: [
    "Placer le cube de sucre sur la cuillère à absinthe perforée au-dessus du verre",
    "Verser l'absinthe dans le verre",
    "Verser lentement l'eau glacée sur le cube de sucre",
    "Remuer délicatement et déguster"
  ]
};

export const RECIPES_MOCK: Recipe[] = [
  RECIPE_MOCK,
  {
    id: 2,
    name: "Mojito Classique",
    description: "Un cocktail rafraîchissant à base de rhum blanc, menthe fraîche et citron vert.",
    picture: "https://images.unsplash.com/photo-1551538827-9c037cb4f32a?w=400",
    ingredients: [
      {
        id: 4,
        ingredient: { id: 20, name: "rhum blanc" },
        quantity: 6,
        unit: "cl"
      },
      {
        id: 5,
        ingredient: { id: 21, name: "citron vert" },
        quantity: 1,
        unit: "pièce"
      },
      {
        id: 6,
        ingredient: { id: 22, name: "menthe fraîche" },
        quantity: 10,
        unit: "feuilles"
      },
      {
        id: 7,
        ingredient: { id: 23, name: "sucre de canne" },
        quantity: 2,
        unit: "cuillères à café"
      },
      {
        id: 8,
        ingredient: { id: 24, name: "eau gazeuse" },
        quantity: 10,
        unit: "cl"
      }
    ],
    instructions: [
      "Couper le citron vert en quartiers",
      "Dans un verre, écraser délicatement la menthe avec le sucre",
      "Ajouter les quartiers de citron vert et écraser légèrement",
      "Ajouter le rhum blanc",
      "Remplir de glace pilée",
      "Compléter avec l'eau gazeuse",
      "Remuer délicatement et décorer avec une branche de menthe"
    ]
  },
  {
    id: 3,
    name: "Margarita",
    description: "Le cocktail mexicain par excellence à base de tequila, triple sec et citron vert.",
    picture: "https://images.unsplash.com/photo-1609951651556-5334e2706168?w=400",
    ingredients: [
      {
        id: 9,
        ingredient: { id: 30, name: "tequila" },
        quantity: 5,
        unit: "cl"
      },
      {
        id: 10,
        ingredient: { id: 31, name: "triple sec" },
        quantity: 2,
        unit: "cl"
      },
      {
        id: 11,
        ingredient: { id: 32, name: "jus de citron vert" },
        quantity: 3,
        unit: "cl"
      },
      {
        id: 12,
        ingredient: { id: 33, name: "sel" },
        quantity: 1,
        unit: "pincée"
      }
    ],
    instructions: [
      "Givrer le bord du verre avec le sel",
      "Dans un shaker avec des glaçons, verser la tequila",
      "Ajouter le triple sec et le jus de citron vert",
      "Shaker énergiquement",
      "Servir dans le verre givré avec des glaçons",
      "Décorer avec une rondelle de citron vert"
    ]
  }
];

// Utilitaire pour simuler un délai d'API
export const delay = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

// Service mock pour le développement
export class MockRecipeService {
  private recipes: Recipe[] = [...RECIPES_MOCK];
  private nextId = 4;

  async getRecipes(): Promise<Recipe[]> {
    await delay(500); // Simule un délai réseau
    return [...this.recipes];
  }

  async getRecipe(id: number): Promise<Recipe> {
    await delay(300);
    const recipe = this.recipes.find(r => r.id === id);
    if (!recipe) {
      throw new Error(`Recette avec l'ID ${id} non trouvée`);
    }
    return { ...recipe };
  }

  async createRecipe(recipeData: any): Promise<Recipe> {
    await delay(800);
    const newRecipe: Recipe = {
      ...recipeData,
      id: this.nextId++
    };
    this.recipes.push(newRecipe);
    return { ...newRecipe };
  }

  async deleteRecipe(id: number): Promise<void> {
    await delay(400);
    const index = this.recipes.findIndex(r => r.id === id);
    if (index === -1) {
      throw new Error(`Recette avec l'ID ${id} non trouvée`);
    }
    this.recipes.splice(index, 1);
  }
}

export const mockRecipeService = new MockRecipeService();
