import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, AppBar, Toolbar, Typography } from '@mui/material';
import RecipeListPage from './pages/RecipeListPage';
import RecipeDetailPage from './pages/RecipeDetailPage';
import AddRecipePage from './pages/AddRecipePage';
import './App.css';

// Thème Material-UI personnalisé
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    h3: {
      fontWeight: 600,
    },
    h4: {
      fontWeight: 600,
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <div className="App">
          {/* Header de l'application */}
          <AppBar position="static" elevation={1}>
            <Toolbar>
              <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                🍹 Application Cocktail
              </Typography>
            </Toolbar>
          </AppBar>

          {/* Contenu principal avec routing */}
          <main>
            <Routes>
              {/* Route par défaut - redirige vers la liste des recettes */}
              <Route path="/" element={<Navigate to="/recipes" replace />} />

              {/* Liste des recettes */}
              <Route path="/recipes" element={<RecipeListPage />} />

              {/* Détail d'une recette */}
              <Route path="/recipe/:id" element={<RecipeDetailPage />} />

              {/* Ajouter une recette */}
              <Route path="/recipe/add" element={<AddRecipePage />} />

              {/* Route 404 - redirige vers la liste */}
              <Route path="*" element={<Navigate to="/recipes" replace />} />
            </Routes>
          </main>
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;
